import { and, or, eq, ne, like, ilike, gte, lte, between, isNull, inArray, SQL } from 'drizzle-orm';
import type { DatabaseConfig } from '@/lib/configCache';

// 类型守卫：验证日期字符串
const isValidDate = (dateString: string | null): dateString is string => {
  if (!dateString) return false;
  const date = new Date(dateString);
  return !Number.isNaN(date.getTime());
};

/**
 * 根据searchParams和config动态构建Drizzle where条件
 * 重构版本：适配 Drizzle ORM 的查询语法
 */
export function buildDrizzleWhere(
  searchParams: URLSearchParams, 
  config: DatabaseConfig,
  tableColumns: Record<string, any>
): SQL | undefined {
  // 通用筛选参数
  const filters: Record<string, unknown> = {};

  // 获取所有参数，包括多值参数
  const processedKeys = new Set<string>();
  searchParams.forEach((value, key) => {
    if (!['page', 'limit', 'sortBy', 'sortOrder', 'allFields'].includes(key) && !processedKeys.has(key)) {
      const allValues = searchParams.getAll(key);
      filters[key] = allValues.length === 1 ? allValues[0] : allValues;
      processedKeys.add(key);
    }
  });

  // 全局搜索关键词
  const globalKeyword = searchParams.get('allFields');

  // 构建 where 条件数组
  const whereConditions: SQL[] = [];

  // 处理每个筛选字段
  for (const [key, value] of Object.entries(filters)) {
    if (!value || (Array.isArray(value) && value.length === 0)) continue;

    // 获取字段配置
    const fieldConfig = config.fields.find(f => f.fieldName === key);
    if (!fieldConfig) continue;

    // 获取对应的表列
    const column = tableColumns[key];
    if (!column) continue;

    // 根据字段类型和搜索类型构建条件
    if (fieldConfig.fieldType === 'date' && fieldConfig.searchType === 'date_range') {
      // 日期范围处理
      if (typeof value === 'string') {
        const [startDate, endDate] = value.split(',');
        if (isValidDate(startDate) && isValidDate(endDate)) {
          whereConditions.push(
            between(column, new Date(startDate), new Date(endDate))
          );
        } else if (isValidDate(startDate)) {
          whereConditions.push(gte(column, new Date(startDate)));
        } else if (isValidDate(endDate)) {
          whereConditions.push(lte(column, new Date(endDate)));
        }
      }
    } else if (fieldConfig.searchType === 'range' && fieldConfig.fieldType === 'number') {
      // 数值范围处理
      if (typeof value === 'string') {
        const [min, max] = value.split(',').map(v => parseFloat(v.trim()));
        if (!isNaN(min) && !isNaN(max)) {
          whereConditions.push(between(column, min, max));
        } else if (!isNaN(min)) {
          whereConditions.push(gte(column, min));
        } else if (!isNaN(max)) {
          whereConditions.push(lte(column, max));
        }
      }
    } else if (fieldConfig.searchType === 'exact') {
      // 精确匹配
      if (Array.isArray(value)) {
        whereConditions.push(inArray(column, value));
      } else {
        whereConditions.push(eq(column, value));
      }
    } else if (fieldConfig.searchType === 'contains' && typeof value === 'string') {
      // 包含匹配（不区分大小写）
      whereConditions.push(ilike(column, `%${value}%`));
    } else if (fieldConfig.searchType === 'starts_with' && typeof value === 'string') {
      // 开头匹配
      whereConditions.push(ilike(column, `${value}%`));
    } else if (fieldConfig.searchType === 'ends_with' && typeof value === 'string') {
      // 结尾匹配
      whereConditions.push(ilike(column, `%${value}`));
    } else if (fieldConfig.fieldType === 'boolean') {
      // 布尔值处理
      whereConditions.push(eq(column, value === 'true'));
    } else {
      // 处理多选和单选字段
      if (Array.isArray(value)) {
        // 多选字段处理
        const hasNullValue = value.includes('N/A');
        if (hasNullValue) {
          const nonNullValues = value.filter(v => v !== 'N/A');
          if (nonNullValues.length > 0) {
            // 既有具体值又有N/A，使用OR条件
            whereConditions.push(
              or(
                inArray(column, nonNullValues),
                isNull(column),
                eq(column, '')
              )!
            );
          } else {
            // 只选择了N/A
            whereConditions.push(
              or(
                isNull(column),
                eq(column, '')
              )!
            );
          }
        } else {
          whereConditions.push(inArray(column, value));
        }
      } else if (value === 'N/A') {
        // 单选字段选择了N/A
        whereConditions.push(
          or(
            isNull(column),
            eq(column, '')
          )!
        );
      } else {
        whereConditions.push(eq(column, value));
      }
    }
  }

  // 处理全局搜索关键词
  if (globalKeyword && globalKeyword.trim()) {
    const keyword = globalKeyword.trim();
    const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');
    
    if (searchableFields.length > 0) {
      const globalSearchConditions = searchableFields
        .map(f => {
          const column = tableColumns[f.fieldName];
          return column ? ilike(column, `%${keyword}%`) : null;
        })
        .filter(Boolean) as SQL[];

      if (globalSearchConditions.length > 0) {
        whereConditions.push(or(...globalSearchConditions)!);
      }
    }
  }

  // 组合所有条件
  if (whereConditions.length === 0) {
    return undefined;
  } else if (whereConditions.length === 1) {
    return whereConditions[0];
  } else {
    return and(...whereConditions);
  }
}

/**
 * 构建排序条件
 * @param sortBy 排序字段
 * @param sortOrder 排序方向
 * @param tableColumns 表列对象
 * @param config 数据库配置
 * @returns 排序条件数组
 */
export function buildDrizzleOrderBy(
  sortBy: string | null,
  sortOrder: 'asc' | 'desc',
  tableColumns: Record<string, any>,
  config: DatabaseConfig
): any[] {
  const orderByConditions: any[] = [];

  // 处理指定的排序字段
  if (sortBy && tableColumns[sortBy]) {
    const column = tableColumns[sortBy];
    orderByConditions.push(
      sortOrder === 'desc' ? { [sortBy]: 'desc' } : { [sortBy]: 'asc' }
    );
  }

  // 处理默认排序配置
  if (config.defaultSort && Array.isArray(config.defaultSort)) {
    for (const sortConfig of config.defaultSort) {
      if (sortConfig.field && tableColumns[sortConfig.field]) {
        // 避免重复排序同一字段
        if (sortBy !== sortConfig.field) {
          orderByConditions.push({
            [sortConfig.field]: sortConfig.order || 'asc'
          });
        }
      }
    }
  }

  // 默认按 id 排序（如果没有其他排序条件）
  if (orderByConditions.length === 0 && tableColumns.id) {
    orderByConditions.push({ id: 'asc' });
  }

  return orderByConditions;
}

/**
 * 构建选择字段
 * @param config 数据库配置
 * @param tableColumns 表列对象
 * @returns 选择字段对象
 */
export function buildDrizzleSelect(
  config: DatabaseConfig,
  tableColumns: Record<string, any>
): Record<string, boolean> {
  const select: Record<string, boolean> = {};
  
  // 获取可见字段
  const visibleFields = config.fields
    .filter(f => f.isVisible)
    .map(f => f.fieldName);

  // 添加可见字段到选择列表
  visibleFields.forEach(fieldName => {
    if (tableColumns[fieldName]) {
      select[fieldName] = true;
    }
  });

  // 主键 id 始终返回
  if (tableColumns.id) {
    select.id = true;
  }

  return select;
}
